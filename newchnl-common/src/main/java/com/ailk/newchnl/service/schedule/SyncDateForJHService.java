package com.ailk.newchnl.service.schedule;

import com.ailk.newchnl.entity.schedule.SyncDateForJH;

import java.util.List;

/**
 * 云电脑终端用户稽核信息入库接口
 */
public interface SyncDateForJHService {
    /**
     * 云电脑终端用户稽核信息数据同步
     *
     * @throws Exception
     */
    void execute() throws Exception;

    /**
     * 执行指定月份的降云电脑终端用户稽核信息数据同步
     *
     * @param billMonth 账期月份，格式：yyyyMM
     * @throws Exception
     */
    void executeAll(String billMonth) throws Exception;

    /**
     * 将云电脑终端用户稽核信息数据入库
     *
     * @param syncDateForJHList 云电脑终端用户稽核信息数据列表
     * @param billMonth          账期月份，格式：yyyyMM
     * @throws Exception
     */
    void toBase(List<SyncDateForJH> syncDateForJHList, String billMonth) throws Exception;
}
