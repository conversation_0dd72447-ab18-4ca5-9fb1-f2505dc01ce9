package com.ailk.newchnl.entity.schedule;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class SyncDateForDTL implements Serializable {
    //执行月份
    private String billMonth;
    //用户标识
    private String userId;
    //手机
    private String phoneNo;
    //当月优惠后套餐费
    private BigDecimal offerFeeDy;
    //下月优惠后套餐费
    private BigDecimal offerFeeCy;
    //当月固定费
    private BigDecimal gudingFeeDy;
    //下月固定费
    private BigDecimal gudingFeeCy;
    //收费业务offer_id
    private Long offerId;
    //收费业务名称
    private String offerName;
    //收费业务价格
    private BigDecimal unitPrice;
    //收费业务办理日期
    private Date createDate;
    //操作员标识
    private Long opId;
    //操作员名称
    private String opName;
    //营业厅标识
    private Long orgId;
    //组织名称
    private String orgName;
    //降档办理日期
    private Date jdCreateDate;
    //降档操作员标识
    private Long jdOpId;
    //降档操作员名称
    private String jdOpName;
    //降档营业厅标识
    private Long jdOrgId;
    //降档组织名称
    private String jdOrgName;
    //业务类型
    private String busiType;

    public String getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public BigDecimal getOfferFeeDy() {
        return offerFeeDy;
    }

    public void setOfferFeeDy(BigDecimal offerFeeDy) {
        this.offerFeeDy = offerFeeDy;
    }

    public BigDecimal getOfferFeeCy() {
        return offerFeeCy;
    }

    public void setOfferFeeCy(BigDecimal offerFeeCy) {
        this.offerFeeCy = offerFeeCy;
    }

    public BigDecimal getGudingFeeDy() {
        return gudingFeeDy;
    }

    public void setGudingFeeDy(BigDecimal gudingFeeDy) {
        this.gudingFeeDy = gudingFeeDy;
    }

    public BigDecimal getGudingFeeCy() {
        return gudingFeeCy;
    }

    public void setGudingFeeCy(BigDecimal gudingFeeCy) {
        this.gudingFeeCy = gudingFeeCy;
    }

    public Long getOfferId() {
        return offerId;
    }

    public void setOfferId(Long offerId) {
        this.offerId = offerId;
    }

    public String getOfferName() {
        return offerName;
    }

    public void setOfferName(String offerName) {
        this.offerName = offerName;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Date getJdCreateDate() {
        return jdCreateDate;
    }

    public void setJdCreateDate(Date jdCreateDate) {
        this.jdCreateDate = jdCreateDate;
    }

    public Long getJdOpId() {
        return jdOpId;
    }

    public void setJdOpId(Long jdOpId) {
        this.jdOpId = jdOpId;
    }

    public String getJdOpName() {
        return jdOpName;
    }

    public void setJdOpName(String jdOpName) {
        this.jdOpName = jdOpName;
    }

    public Long getJdOrgId() {
        return jdOrgId;
    }

    public void setJdOrgId(Long jdOrgId) {
        this.jdOrgId = jdOrgId;
    }

    public String getJdOrgName() {
        return jdOrgName;
    }

    public void setJdOrgName(String jdOrgName) {
        this.jdOrgName = jdOrgName;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }
}
