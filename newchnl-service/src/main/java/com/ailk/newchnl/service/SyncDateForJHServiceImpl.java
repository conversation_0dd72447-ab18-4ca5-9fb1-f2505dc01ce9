package com.ailk.newchnl.service;

import com.ailk.newchnl.entity.schedule.SyncDateForJH;
import com.ailk.newchnl.service.schedule.SyncDateForDTLServiceImpl;
import com.ailk.newchnl.service.schedule.SyncDateForJHService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 云电脑终端用户稽核信息入库接口实现
 */
@Service("SyncDateForJHService")
public class SyncDateForJHServiceImpl implements SyncDateForJHService {
    private static final Logger logger = LoggerFactory.getLogger(SyncDateForDTLServiceImpl.class);

    // 服务端ip地址
    private static String ftpIp = "";
    // 用户名
    private static String ftpUserName = "";
    // 密码
    private static String ftpPassword = "";
    // 经分存放文件路径
    private static String remotePath = "";
    // 渠道存放文件的路径
    private static String url_Loca = "";
    // 渠道ip
    private static String localIp = "";
    // 渠道用户名
    private static String localUserName = "";
    // 渠道密码
    private static String localPassword = "";

    @Resource
    private SyncDateForJHService syncDateForJHService;


    @Override
    public void execute() throws Exception {

    }

    @Override
    public void executeAll(String billMonth) throws Exception {

    }

    @Override
    public void toBase(List<SyncDateForJH> syncDateForJHList, String billMonth) throws Exception {

    }
}
