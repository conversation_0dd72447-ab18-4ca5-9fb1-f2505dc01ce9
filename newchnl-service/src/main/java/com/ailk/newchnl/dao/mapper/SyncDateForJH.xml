<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ailk.newchnl.dao.SyncDateForJHDao">
    <insert id="insert" parameterType="com.ailk.newchnl.entity.schedule.SyncDateForJH">
        insert into newqdgl.ST_MKT_LANTERM_JH_${billMonth}
            (bill_month, user_id, phone_no, offer_name, eff_date)
        values (#{billMonth,jdbcType=VARCHAR},
                #{userId,jdbcType=VARCHAR},
                #{phoneNo,jdbcType=VARCHAR},
                #{offerName,jdbcType=VARCHAR},
                #{effDate,jdbcType=DATE})
    </insert>

    <select id="queryInfo" parameterType="map" resultType="Long">
        select count(*)
        from ST_MKT_LANTERM_JH_${map.billMonth}
    </select>
</mapper>