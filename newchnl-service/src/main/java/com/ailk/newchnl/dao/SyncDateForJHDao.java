package com.ailk.newchnl.dao;

import com.ailk.newchnl.dao.impl.BaseDao;
import com.ailk.newchnl.entity.schedule.SyncDateForJH;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository("SyncDateForJHDao")
public interface SyncDateForJHDao extends BaseDao<SyncDateForJH> {
    /**
     * 查询云电脑终端用户稽核信息数据是否已入库
     * @param map 包含billMonth参数
     * @return 记录数
     * @throws Exception
     */
    Long queryInfo(@Param("map") Map map) throws Exception;
}
