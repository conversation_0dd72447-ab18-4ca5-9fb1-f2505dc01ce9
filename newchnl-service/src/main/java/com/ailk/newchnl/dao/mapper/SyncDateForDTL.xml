<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ailk.newchnl.dao.SyncDateForDTLDao">
    <insert id="insert" parameterType="com.ailk.newchnl.entity.schedule.SyncDateForDTL">
        insert into newqdgl.st_mkt_jiangdang_busi_dtl_${billMonth}
        (bill_month, user_id, phone_no, offer_fee_dy, offer_fee_cy, guding_fee_dy, guding_fee_cy,
         offer_id, offer_name, unit_price, create_date, op_id, op_name, org_id, org_name,
         jd_create_date, jd_op_id, jd_op_name, jd_org_id, jd_org_name, busi_type)
        values (#{billMonth,jdbcType=VARCHAR},
                #{userId,jdbcType=VARCHAR}, #{phoneNo,jdbcType=VARCHAR},
                #{offerFeeDy,jdbcType=DECIMAL}, #{offerFeeCy,jdbcType=DECIMAL},
                #{gudingFeeDy,jdbcType=DECIMAL}, #{gudingFeeCy,jdbcType=DECIMAL},
                #{offerId,jdbcType=BIGINT}, #{offerName,jdbcType=VARCHAR},
                #{unitPrice,jdbcType=DECIMAL}, #{createDate,jdbcType=TIMESTAMP},
                #{opId,jdbcType=BIGINT}, #{opName,jdbcType=VARCHAR},
                #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR},
                #{jdCreateDate,jdbcType=TIMESTAMP}, #{jdOpId,jdbcType=BIGINT},
                #{jdOpName,jdbcType=VARCHAR},
                #{jdOrgId,jdbcType=BIGINT}, #{jdOrgName,jdbcType=VARCHAR},
                #{busiType,jdbcType=VARCHAR})
    </insert>

    <select id="queryInfo" parameterType="map" resultType="Long">
        select count(*)
        from st_mkt_jiangdang_busi_dtl_${map.billMonth}
    </select>
</mapper>