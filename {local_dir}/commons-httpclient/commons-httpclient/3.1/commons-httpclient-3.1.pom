<?xml version="1.0" encoding="UTF-8"?><project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-httpclient</groupId>
  <artifactId>commons-httpclient</artifactId>
  <name>HttpClient</name>
  <version>3.1</version>
  <description>The HttpClient  component supports the client-side of RFC 1945 (HTTP/1.0)  and RFC 2616 (HTTP/1.1) , several related specifications (RFC 2109 (Cookies) , RFC 2617 (HTTP Authentication) , etc.), and provides a framework by which new request types (methods) or HTTP extensions can be created easily.</description>
  <url>http://jakarta.apache.org/httpcomponents/httpclient-3.x/</url>
  <issueManagement>
    <url>http://issues.apache.org/jira/browse/HTTPCLIENT</url>
  </issueManagement>
  <ciManagement>
    <notifiers>
      <notifier>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>
  <inceptionYear>2001</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>HttpComponents Developer List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/jakarta-httpcomponents-dev/</archive>
    </mailingList>
    <mailingList>
      <name>HttpClient User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/jakarta-httpclient-user/</archive>
    </mailingList>
  </mailingLists>
  <developers>
    <developer>
      <id>mbecke</id>
      <name>Michael Becke</name>
      <email>mbecke -at- apache.org</email>
      <organization></organization>
      <roles>
        <role>Release Prime</role>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jsdever</id>
      <name>Jeff Dever</name>
      <email>jsdever -at- apache.org</email>
      <organization>Independent consultant</organization>
      <roles>
        <role>2.0 Release Prime</role>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>dion</id>
      <name>dIon Gillard</name>
      <email>dion -at- apache.org</email>
      <organization>Multitask Consulting</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>oglueck</id>
      <name>Ortwin Glueck</name>
      <email>oglueck -at- apache.org</email>
      <url>http://www.odi.ch/</url>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jericho</id>
      <name>Sung-Gu</name>
      <email>jericho -at- apache.org</email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>olegk</id>
      <name>Oleg Kalnichevski</name>
      <email>olegk -at- apache.org</email>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>sullis</id>
      <name>Sean C. Sullivan</name>
      <email>sullis -at- apache.org</email>
      <organization>Independent consultant</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>adrian</id>
      <name>Adrian Sutton</name>
      <email>adrian.sutton -at- ephox.com</email>
      <organization>Intencha</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>rwaldhoff</id>
      <name>Rodney Waldhoff</name>
      <email>rwaldhoff -at- apache</email>
      <organization>Britannica</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name>Armando Anton</name>
      <email>armando.anton -at- newknow.com</email>
    </contributor>
    <contributor>
      <name>Sebastian Bazley</name>
      <email>sebb -at- apache.org</email>
    </contributor>
    <contributor>
      <name>Ola Berg</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Sam Berlin</name>
      <email>sberlin -at- limepeer.com</email>
    </contributor>
    <contributor>
      <name>Mike Bowler</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Samit Jain</name>
      <email>jain.samit -at- gmail.com</email>
    </contributor>
    <contributor>
      <name>Eric Johnson</name>
      <email>eric -at- tibco.com</email>
    </contributor>
    <contributor>
      <name>Christian Kohlschuetter</name>
      <email>ck -at- newsclub.de</email>
    </contributor>
    <contributor>
      <name>Ryan Lubke</name>
      <email>Ryan.Lubke -at- Sun.COM</email>
    </contributor>
    <contributor>
      <name>Sam Maloney</name>
      <email>sam.maloney -at- filogix.com</email>
    </contributor>
    <contributor>
      <name>Rob Di Marco</name>
      <email>rdimarco -at- hmsonline.com</email>
    </contributor>
    <contributor>
      <name>Juergen Pill</name>
      <email>Juergen.Pill -at- softwareag.com</email>
    </contributor>
    <contributor>
      <name>Mohammad Rezaei</name>
      <email>mohammad.rezaei -at- gs.com</email>
    </contributor>
    <contributor>
      <name>Roland Weber</name>
      <email>rolandw -at- apache.org</email>
    </contributor>
    <contributor>
      <name>Laura Werner</name>
      <email>laura -at- lwerner.org</email>
    </contributor>
    <contributor>
      <name>Mikael Wilstrom</name>
      <email>mikael.wikstrom -at- it.su.se</email>
    </contributor>
  </contributors>
  <licenses>
    <license>
      <name>Apache License</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/jakarta/httpcomponents/oac.hc3x/trunk</connection>
    <url>http://svn.apache.org/repos/asf/jakarta/httpcomponents/oac.hc3x/trunk</url>
  </scm>
  <organization>
    <name>Apache Software Foundation</name>
    <url>http://jakarta.apache.org/</url>
  </organization>
  <build>
    <sourceDirectory>src/java</sourceDirectory>
    <testSourceDirectory>src/test</testSourceDirectory>
    <resources>
      <resource>
        <directory>src/resources</directory>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>src/test</directory>
        <includes>
          <include>**/*.keystore</include>
        </includes>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <includes>
            <include>**/TestAll.java</include>
          </includes>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.0.4</version>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.2</version>
    </dependency>
  </dependencies>
  <distributionManagement>
    <site>
      <id>default</id>
      <name>Default Site</name>
      <url>scp://people.apache.org//www/jakarta.apache.org/httpcomponents/httpclient-3.x/</url>
    </site>
    <status>converted</status>
  </distributionManagement>
</project>