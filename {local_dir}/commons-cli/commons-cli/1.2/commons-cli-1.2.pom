<?xml version="1.0" encoding="UTF-8"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>11</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-cli</groupId>
  <artifactId>commons-cli</artifactId>
  <version>1.2</version>
  <name>Commons CLI</name>

  <inceptionYear>2002</inceptionYear>
  <description>
    Commons CLI provides a simple API for presenting, processing and validating a command line interface.
  </description>

  <url>http://commons.apache.org/cli/</url>

  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/CLI</url>
  </issueManagement>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/cli/branches/cli-1.x/</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/cli/branches/cli-1.x/</developerConnection>
    <url>http://svn.apache.org/viewvc/commons/proper/cli/branches/cli-1.x/</url>
  </scm>

  <developers>
    <developer>
      <name>James Strachan</name>
      <id>jstrachan</id>
      <email><EMAIL></email>
      <organization>SpiritSoft, Inc.</organization>
    </developer>
    <developer>
      <name>Bob McWhirter</name>
      <id>bob</id>
      <email><EMAIL></email>
      <organization>Werken</organization>
      <roles>
        <role>contributed ideas and code from werken.opt</role>
      </roles>
    </developer>
    <developer>
      <name>John Keyes</name>
      <id>jkeyes</id>
      <email><EMAIL></email>
      <organization>integral Source</organization>
      <roles>
        <role>contributed ideas and code from Optz</role>
      </roles>
    </developer>
    <developer>
      <name>Rob Oxspring</name>
      <id>roxspring</id>
      <email><EMAIL></email>
      <organization>Indigo Stone</organization>
      <roles>
        <role>designed CLI2</role>
      </roles>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Peter Donald</name>
      <roles>
        <role>contributed ideas and code from Avalon Excalibur's cli package</role>
      </roles>
    </contributor>
    <contributor>
      <name>Brian Egge</name>
      <roles>
        <role>made the 1.1 release happen</role>
      </roles>
    </contributor>
    <contributor>
      <name>Berin Loritsch</name>
      <email><EMAIL></email>
      <roles>
        <role>helped in the Avalon CLI merge</role>
      </roles>
    </contributor>
    <contributor>
      <name>Peter Maddocks</name>
      <email><EMAIL></email>
      <organization>Hewlett-Packard</organization>
      <roles>
        <role>supplied patch</role>
      </roles>
    </contributor>
    <contributor>
      <name>Andrew Shirley</name>
      <roles>
        <role>lots of fixes for 1.1</role>
      </roles>
    </contributor>
  </contributors>

  <dependencies>
    <!-- used for unit tests -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <properties>
    <maven.compile.source>1.4</maven.compile.source>
    <maven.compile.target>1.4</maven.compile.target>
    <commons.componentid>cli</commons.componentid>
    <commons.release.version>1.2</commons.release.version>
    <commons.release.name>commons-cli-${commons.release.version}</commons.release.name>
    <commons.osgi.symbolicName>org.apache.commons.cli</commons.osgi.symbolicName>
    <commons.binary.suffix />
    <commons.jira.id>CLI</commons.jira.id>
    <commons.jira.pid>12310463</commons.jira.pid>
    <!-- The RC version used in the staging repository URL. -->
    <commons.rc.version>RC7</commons.rc.version>    
  </properties>

  <build>
    <sourceDirectory>src/java</sourceDirectory>
    <testSourceDirectory>src/test</testSourceDirectory>
    <resources>
      <resource>
        <directory>.</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>NOTICE.txt</include>
          <include>LICENSE.txt</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/bin.xml</descriptor>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <!--
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-ant-plugin</artifactId>
        <version>2.1.1-SNAPSHOT</version>
      </plugin>
      -->
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>1.2</version>
        <configuration>
          <threshold>Normal</threshold>
          <effort>Default</effort>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <configLocation>src/conf/checkstyle.xml</configLocation>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <version>2.2</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <version>2.2.1</version>
        <configuration>
          <comparisonVersion>1.1</comparisonVersion>
        </configuration>
      </plugin>
    </plugins>
  </reporting>


  <profiles>
    <!-- Profile to let Gump choose the name of the jar file generated -->
    <profile>
      <id>gump</id>
      <activation>
        <property>
          <name>maven.final.name</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-jar-plugin</artifactId>
            <configuration>
              <finalName>${maven.final.name}</finalName>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>rc</id>
      <distributionManagement>
        <!-- Cannot define in parent ATM, see COMMONSSITE-26 -->
        <site>
          <id>apache.website</id>
          <name>Apache Commons Release Candidate Staging Site</name>
          <url>${commons.deployment.protocol}://people.apache.org/www/people.apache.org/builds/commons/${commons.componentid}/${commons.release.version}/${commons.rc.version}/site</url>
        </site>
      </distributionManagement>
    </profile>
  </profiles>

</project>
