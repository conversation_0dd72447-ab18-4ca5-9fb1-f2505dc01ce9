<project>
    <modelVersion>4.0.0</modelVersion>
    <groupId>backport-util-concurrent</groupId>
    <artifactId>backport-util-concurrent</artifactId>
    <version>3.1</version>
    <packaging>jar</packaging>
    <name>Backport of JSR 166</name>
    <url>http://backport-jsr166.sourceforge.net/</url>
    <description><PERSON><PERSON><PERSON>'s backport of JSR 166</description>
    <licenses>
        <license>
            <name>Public Domain</name>
            <url>http://creativecommons.org/licenses/publicdomain</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <url>svn://dcl.mathcs.emory.edu/software/harness2/trunk/util/backport-util-concurrent/</url>
    </scm>
    <organization>
        <name><PERSON><PERSON><PERSON></name>
        <url>http://www.mathcs.emory.edu/~dawidk/</url>
    </organization>
    <dependencies/>
</project>
