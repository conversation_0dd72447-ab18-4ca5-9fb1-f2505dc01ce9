<?xml version="1.0" encoding="UTF-8"?><project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-lang</groupId>
  <artifactId>commons-lang</artifactId>
  <name>Lang</name>
  <version>2.3</version>
  <description>Commons.Lang, a package of Java utility classes for the
        classes that are in java.lang's hierarchy, or are considered to be so
        standard as to justify existence in java.lang.</description>
  <url>http://jakarta.apache.org/commons/lang/</url>
  <issueManagement>
    <url>http://issues.apache.org/jira/</url>
  </issueManagement>
  <ciManagement>
    <notifiers>
      <notifier>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>
  <inceptionYear>2001</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>Commons Dev List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/eyebrowse/SummarizeList?listName=<EMAIL></archive>
    </mailingList>
    <mailingList>
      <name>Commons User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/eyebrowse/SummarizeList?listName=<EMAIL></archive>
    </mailingList>
  </mailingLists>
  <developers>
    <developer>
      <id>dlr</id>
      <name>Daniel Rall</name>
      <email><EMAIL></email>
      <organization>CollabNet, Inc.</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>scolebourne</id>
      <name>Stephen Colebourne</name>
      <email><EMAIL></email>
      <organization>SITA ATS Ltd</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>0</timezone>
    </developer>
    <developer>
      <id>bayard</id>
      <name>Henri Yandell</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>scaswell</id>
      <name>Steven Caswell</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>rdonkin</id>
      <name>Robert Burrell Donkin</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>ggregory</id>
      <name>Gary D. Gregory</name>
      <email><EMAIL></email>
      <organization>Seagull Software</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>psteitz</id>
      <name>Phil Steitz</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>fredrik</id>
      <name>Fredrik Westermarck</name>
      <email></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jcarman</id>
      <name>James Carman</name>
      <email><EMAIL></email>
      <organization>Carman Consulting, Inc.</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name>C. Scott Ananian</name>
    </contributor>
    <contributor>
      <name>Chris Audley</name>
    </contributor>
    <contributor>
      <name>Stephane Bailliez</name>
    </contributor>
    <contributor>
      <name>Michael Becke</name>
    </contributor>
    <contributor>
      <name>Ola Berg</name>
    </contributor>
    <contributor>
      <name>Nathan Beyer</name>
    </contributor>
    <contributor>
      <name>Stefan Bodewig</name>
    </contributor>
    <contributor>
      <name>Janek Bogucki</name>
    </contributor>
    <contributor>
      <name>Mike Bowler</name>
    </contributor>
    <contributor>
      <name>Sean Brown</name>
    </contributor>
    <contributor>
      <name>Alexander Day Chaffee</name>
    </contributor>
    <contributor>
      <name>Al Chou</name>
    </contributor>
    <contributor>
      <name>Greg Coladonato</name>
    </contributor>
    <contributor>
      <name>Maarten Coene</name>
    </contributor>
    <contributor>
      <name>Justin Couch</name>
    </contributor>
    <contributor>
      <name>Michael Davey</name>
    </contributor>
    <contributor>
      <name>Norm Deane</name>
    </contributor>
    <contributor>
      <name>Ringo De Smet</name>
    </contributor>
    <contributor>
      <name>Russel Dittmar</name>
    </contributor>
    <contributor>
      <name>Steve Downey</name>
    </contributor>
    <contributor>
      <name>Matthias Eichel</name>
    </contributor>
    <contributor>
      <name>Christopher Elkins</name>
    </contributor>
    <contributor>
      <name>Chris Feldhacker</name>
    </contributor>
    <contributor>
      <name>Pete Gieser</name>
    </contributor>
    <contributor>
      <name>Jason Gritman</name>
    </contributor>
    <contributor>
      <name>Matthew Hawthorne</name>
    </contributor>
    <contributor>
      <name>Michael Heuer</name>
    </contributor>
    <contributor>
      <name>Oliver Heger</name>
    </contributor>
    <contributor>
      <name>Chris Hyzer</name>
    </contributor>
    <contributor>
      <name>Marc Johnson</name>
    </contributor>
    <contributor>
      <name>Shaun Kalley</name>
    </contributor>
    <contributor>
      <name>Tetsuya Kaneuchi</name>
    </contributor>
    <contributor>
      <name>Nissim Karpenstein</name>
    </contributor>
    <contributor>
      <name>Ed Korthof</name>
    </contributor>
    <contributor>
      <name>Holger Krauth</name>
    </contributor>
    <contributor>
      <name>Rafal Krupinski</name>
    </contributor>
    <contributor>
      <name>Rafal Krzewski</name>
    </contributor>
    <contributor>
      <name>Craig R. McClanahan</name>
    </contributor>
    <contributor>
      <name>Rand McNeely</name>
    </contributor>
    <contributor>
      <name>Dave Meikle</name>
    </contributor>
    <contributor>
      <name>Nikolay Metchev</name>
    </contributor>
    <contributor>
      <name>Kasper Nielsen</name>
    </contributor>
    <contributor>
      <name>Tim O'Brien</name>
    </contributor>
    <contributor>
      <name>Brian S O'Neill</name>
    </contributor>
    <contributor>
      <name>Andrew C. Oliver</name>
    </contributor>
    <contributor>
      <name>Alban Peignier</name>
    </contributor>
    <contributor>
      <name>Moritz Petersen</name>
    </contributor>
    <contributor>
      <name>Dmitri Plotnikov</name>
    </contributor>
    <contributor>
      <name>Neeme Praks</name>
    </contributor>
    <contributor>
      <name>Eric Pugh</name>
    </contributor>
    <contributor>
      <name>Stephen Putman</name>
    </contributor>
    <contributor>
      <name>Travis Reeder</name>
    </contributor>
    <contributor>
      <name>Antony Riley</name>
    </contributor>
    <contributor>
      <name>Scott Sanders</name>
    </contributor>
    <contributor>
      <name>Ralph Schaer</name>
    </contributor>
    <contributor>
      <name>Henning P. Schmiedehausen</name>
    </contributor>
    <contributor>
      <name>Sean Schofield</name>
    </contributor>
    <contributor>
      <name>Reuben Sivan</name>
    </contributor>
    <contributor>
      <name>Ville Skytta</name>
    </contributor>
    <contributor>
      <name>Jan Sorensen</name>
    </contributor>
    <contributor>
      <name>Glen Stampoultzis</name>
    </contributor>
    <contributor>
      <name>Scott Stanchfield</name>
    </contributor>
    <contributor>
      <name>Jon S. Stevens</name>
    </contributor>
    <contributor>
      <name>Sean C. Sullivan</name>
    </contributor>
    <contributor>
      <name>Ashwin Suresh</name>
    </contributor>
    <contributor>
      <name>Helge Tesgaard</name>
    </contributor>
    <contributor>
      <name>Arun Mammen Thomas</name>
    </contributor>
    <contributor>
      <name>Masato Tezuka</name>
    </contributor>
    <contributor>
      <name>Jeff Varszegi</name>
    </contributor>
    <contributor>
      <name>Chris Webb</name>
    </contributor>
    <contributor>
      <name>Mario Winterer</name>
    </contributor>
    <contributor>
      <name>Stepan Koltsov</name>
    </contributor>
    <contributor>
      <name>Holger Hoffstatte</name>
    </contributor>
  </contributors>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>/LICENSE.txt</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/jakarta/commons/proper/lang/trunk</connection>
    <url>http://svn.apache.org/viewvc/jakarta/commons/proper/lang/trunk</url>
  </scm>
  <organization>
    <name>The Apache Software Foundation</name>
    <url>http://jakarta.apache.org</url>
  </organization>
  <build>
    <sourceDirectory>src/java</sourceDirectory>
    <testSourceDirectory>src/test</testSourceDirectory>
    <resources>
      <resource>
        <targetPath>META-INF</targetPath>
        <directory>.</directory>
        <includes>
          <include>NOTICE.txt</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <includes>
            <include>**/*TestSuite.java</include>
          </includes>
          <excludes>
            <exclude>**/AllLangTestSuite.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>maven-plugins</groupId>
        <artifactId>maven-cobertura-plugin</artifactId>
        <version>1.1.1</version>
        <configuration>
          <scope>test</scope>
          <comment>Required only for generating test coverage reports.</comment>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <distributionManagement>
    <repository>
      <id>default</id>
      <name>Default Repository</name>
      <url>file:///www/jakarta.apache.org/builds/jakarta-commons/lang/</url>
    </repository>
    <site>
      <id>default</id>
      <name>Default Site</name>
      <url>scp://people.apache.org//www/jakarta.apache.org/commons/lang/</url>
    </site>
    <status>converted</status>
  </distributionManagement>
</project>